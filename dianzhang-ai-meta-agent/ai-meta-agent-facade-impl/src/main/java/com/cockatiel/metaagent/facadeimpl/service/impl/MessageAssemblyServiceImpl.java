package com.cockatiel.metaagent.facadeimpl.service.impl;

import com.cockatiel.metaagent.facadeimpl.service.MessageAssemblyService;
import com.cockatiel.metaagent.infrastructure.model.entity.AiMessage;
import com.cockatiel.metaagent.infrastructure.model.entity.MessageNode;
import com.cockatiel.metaagent.infrastructure.model.message.MessageDTO;
import com.cockatiel.metaagent.infrastructure.repository.AiMessageRepository;
import com.cockatiel.metaagent.infrastructure.repository.MessageNodeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息组装服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageAssemblyServiceImpl implements MessageAssemblyService {

    @Resource
    private AiMessageRepository aiMessageRepository;
    @Resource
    private MessageNodeRepository messageNodeRepository;

    @Override
    public List<MessageDTO> assembleMessagesByConversationId(String conversationId, Integer limit, Integer offset) {
        log.info("开始组装会话消息，conversationId: {}, limit: {}, offset: {}", conversationId, limit, offset);
        
        // 1. 查询会话中的消息列表
        List<AiMessage> messages = aiMessageRepository.findByConversationId(conversationId, limit, offset);
        if (CollectionUtils.isEmpty(messages)) {
            log.info("会话中没有找到消息，conversationId: {}", conversationId);
            return new ArrayList<>();
        }

        // 2. 组装完整的消息DTO列表
        List<MessageDTO> messageDTOs = new ArrayList<>();
        for (AiMessage message : messages) {
            MessageDTO messageDTO = convertToMessageDTO(message);
            
            // 获取该消息的节点列表
            List<MessageNode> nodes = messageNodeRepository.findByMessageId(message.getId());
            
            // 按eventId排序并转换为DTO
            List<MessageDTO.MessageNodeDTO> nodeDTOs = nodes.stream()
                    .sorted((n1, n2) -> Integer.compare(n1.getEventId(), n2.getEventId()))
                    .map(this::convertToMessageNodeDTO)
                    .collect(Collectors.toList());
            
            messageDTO.setNodeList(nodeDTOs);
            messageDTOs.add(messageDTO);
        }

        log.info("成功组装消息列表，数量: {}", messageDTOs.size());
        return messageDTOs;
    }

    @Override
    public MessageDTO assembleMessageById(Long messageId) {
        log.info("开始组装单个消息，messageId: {}", messageId);
        
        // 1. 查询消息基本信息
        AiMessage message = aiMessageRepository.findByMessageId(messageId);
        if (message == null) {
            log.warn("消息不存在，messageId: {}", messageId);
            return null;
        }

        MessageDTO messageDTO = convertToMessageDTO(message);

        // 2. 查询消息的所有节点
        List<MessageNode> nodes = messageNodeRepository.findByMessageId(messageId);
        
        // 3. 转换节点为DTO并排序
        List<MessageDTO.MessageNodeDTO> nodeDTOs = nodes.stream()
                .sorted((n1, n2) -> Integer.compare(n1.getEventId(), n2.getEventId()))
                .map(this::convertToMessageNodeDTO)
                .collect(Collectors.toList());

        messageDTO.setNodeList(nodeDTOs);

        log.info("成功组装单个消息，节点数量: {}", nodeDTOs.size());
        return messageDTO;
    }

    /**
     * 将AiMessage转换为MessageDTO
     */
    private MessageDTO convertToMessageDTO(AiMessage aiMessage) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageId(aiMessage.getId());
        messageDTO.setRole(aiMessage.getRole());
        messageDTO.setTitle(aiMessage.getTitle());
        messageDTO.setStatus(aiMessage.getStatus());
        return messageDTO;
    }

    /**
     * 将MessageNode转换为MessageNodeDTO
     */
    private MessageDTO.MessageNodeDTO convertToMessageNodeDTO(MessageNode messageNode) {
        MessageDTO.MessageNodeDTO nodeDTO = new MessageDTO.MessageNodeDTO();
        nodeDTO.setNodeId(messageNode.getNodeId());
        nodeDTO.setMediaType(messageNode.getMediaType());
        nodeDTO.setContent(messageNode.getContent());
        return nodeDTO;
    }

} 