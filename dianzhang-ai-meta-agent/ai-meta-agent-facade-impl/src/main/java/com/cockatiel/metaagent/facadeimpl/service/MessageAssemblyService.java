package com.cockatiel.metaagent.facadeimpl.service;

import com.cockatiel.metaagent.infrastructure.model.message.MessageDTO;

import java.util.List;

/**
 * 消息组装服务接口
 * 用于将AiMessage和MessageNode组装成完整的MessageDTO
 * 
 * <AUTHOR>
 */
public interface MessageAssemblyService {

    /**
     * 根据会话ID组装完整的消息列表
     * 
     * @param conversationId 会话ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 完整的消息DTO列表
     */
    List<MessageDTO> assembleMessagesByConversationId(String conversationId, Integer limit, Integer offset);

    /**
     * 根据消息ID组装单个完整消息
     * 
     * @param messageId 消息ID
     * @return 完整的消息DTO
     */
    MessageDTO assembleMessageById(Long messageId);
} 