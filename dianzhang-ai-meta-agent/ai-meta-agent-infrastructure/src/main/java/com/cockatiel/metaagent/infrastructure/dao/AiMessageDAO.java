package com.cockatiel.metaagent.infrastructure.dao;

import cn.techwolf.jade.annotation.DAO;
import cn.techwolf.jade.annotation.SQL;
import com.cockatiel.metaagent.infrastructure.model.entity.AiMessage;

import java.util.List;

/**
 * AI消息数据访问层
 * 
 * <AUTHOR>
 */
@DAO(catalog = "blue_ai_agent")
public interface AiMessageDAO {

    String TABLE_NAME = "dz_ai_message";

    String INSERT_COLUMNS = " id, conversation_id, role, title, status ";

    String ALL_COLUMNS = "create_time, update_time, " + INSERT_COLUMNS;

    @SQL("insert into " + TABLE_NAME + " (" + INSERT_COLUMNS + ") values (:1.id, :1.conversationId, :1.role, :1.title, :1.status) ")
    void insert(AiMessage aiMessage);

    @SQL("update " + TABLE_NAME + " set title = :2, status = :3, update_time = NOW() where id = :1 ")
    void updateTitleAndStatus(Long messageId, String title, Integer status);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where id = :1 ")
    AiMessage findByMessageId(Long messageId);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where conversation_id = :1 order by create_time asc limit :3 offset :2 ")
    List<AiMessage> findByConversationId(String conversationId, Integer offset, Integer limit);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where conversation_id = :1 order by create_time desc limit 1 ")
    AiMessage findLastMessageByConversationId(String conversationId);

    @SQL("delete from " + TABLE_NAME + " where id = :1 ")
    void deleteByMessageId(Long messageId);

    @SQL("delete from " + TABLE_NAME + " where conversation_id = :1 ")
    int deleteByConversationId(String conversationId);

    @SQL("select count(*) from " + TABLE_NAME + " where conversation_id = :1 ")
    Long countByConversationId(String conversationId);

    @SQL("select id from " + TABLE_NAME + " where conversation_id = :1 order by create_time asc ")
    List<Long> findMessageIdsByConversationId(String conversationId);
} 