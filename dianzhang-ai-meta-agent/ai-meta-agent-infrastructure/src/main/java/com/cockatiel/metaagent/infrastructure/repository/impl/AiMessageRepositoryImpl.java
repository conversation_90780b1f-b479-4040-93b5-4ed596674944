package com.cockatiel.metaagent.infrastructure.repository.impl;

import com.cockatiel.metaagent.infrastructure.dao.AiMessageDAO;
import com.cockatiel.metaagent.infrastructure.model.entity.AiMessage;
import com.cockatiel.metaagent.infrastructure.repository.AiMessageRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * AI消息仓库数据库实现类
 * 使用数据库持久化存储
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository("aiMessageRepositoryDatabase")
public class AiMessageRepositoryImpl implements AiMessageRepository {

    @Resource
    private AiMessageDAO aiMessageDAO;

    @Override
    public AiMessage save(AiMessage message) {
        // 检查消息是否已存在
        AiMessage existingMessage = aiMessageDAO.findByMessageId(message.getId());
        if (existingMessage == null) {
            aiMessageDAO.insert(message);
            log.debug("成功插入新消息，messageId: {}", message.getId());
        } else {
            boolean needUpdate = !Objects.equals(existingMessage.getTitle(), message.getTitle())
                     || !Objects.equals(existingMessage.getStatus(), message.getStatus());
            if (needUpdate) {
                aiMessageDAO.updateTitleAndStatus(message.getId(), message.getTitle(), message.getStatus());
                log.debug("成功更新消息，messageId: {}", message.getId());
            }
        }
        return message;
    }

    @Override
    public AiMessage findByMessageId(Long messageId) {
        return aiMessageDAO.findByMessageId(messageId);
    }

    @Override
    public List<AiMessage> findByConversationId(String conversationId, Integer limit, Integer offset) {
        log.debug("查找会话消息列表，conversationId: {}, limit: {}, offset: {}", conversationId, limit, offset);
        
        // 设置默认值
        int actualLimit = (limit != null && limit > 0) ? limit : 20;
        int actualOffset = (offset != null && offset >= 0) ? offset : 0;
        
        return aiMessageDAO.findByConversationId(conversationId, actualOffset, actualLimit);
    }

    @Override
    public AiMessage findLastMessageByConversationId(String conversationId) {
        log.debug("查找会话最后一条消息，conversationId: {}", conversationId);
        
        return aiMessageDAO.findLastMessageByConversationId(conversationId);
    }

    @Override
    public boolean deleteByMessageId(Long messageId) {
        log.debug("删除消息，messageId: {}", messageId);
        
        try {
            aiMessageDAO.deleteByMessageId(messageId);
            log.debug("成功删除消息，messageId: {}", messageId);
            return true;
        } catch (Exception e) {
            log.error("删除消息失败，messageId: {}", messageId, e);
            return false;
        }
    }

    @Override
    public int deleteByConversationId(String conversationId) {
        log.debug("删除会话中的所有消息，conversationId: {}", conversationId);
        
        int deletedCount = aiMessageDAO.deleteByConversationId(conversationId);
        log.debug("成功删除会话消息，数量: {}", deletedCount);
        return deletedCount;
    }

    @Override
    public Long countByConversationId(String conversationId) {
        log.debug("统计会话消息数量，conversationId: {}", conversationId);
        
        Long count = aiMessageDAO.countByConversationId(conversationId);
        log.debug("会话消息数量: {}", count);
        return count;
    }

    @Override
    public List<Long> findMessageIdsByConversationId(String conversationId) {
        log.debug("查找会话消息ID列表，conversationId: {}", conversationId);
        
        return aiMessageDAO.findMessageIdsByConversationId(conversationId);
    }
} 